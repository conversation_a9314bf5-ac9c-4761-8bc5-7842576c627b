# SSL Error Fix Documentation

## Problem Description

The worker system was experiencing sporadic SSL errors when connecting to Supabase:
```
[SSL: DECRYPTION_FAILED_OR_BAD_RECORD_MAC] decryption failed or bad record mac (_ssl.c:2559)
httpcore.WriteError: EOF occurred in violation of protocol (_ssl.c:2406)
```

These errors occurred randomly during database operations, particularly in the `get_task_result_file_paths` and `get_file_paths` functions and other database access points.

## Root Cause Analysis

The SSL errors were caused by:

1. **Shared SSL Connections**: The global Supabase client was being shared across multiple processes/threads in the multiprocessing environment
2. **Connection Reuse Issues**: SSL connections becoming stale or corrupted when reused across process boundaries
3. **No Retry Mechanism**: Failed operations were not automatically retried, causing tasks to fail permanently

## Solution Implementation

### 1. Thread-Local Supabase Clients

**File: `pp-worker/database.py`**

- Replaced global `supabaseclient` with thread-local clients using `threading.local()`
- Added `get_supabase_client()` function that creates a new client per thread/process
- Each worker process now gets its own dedicated Supabase connection

```python
def get_supabase_client():
    """Get a thread-local Supabase client to avoid SSL connection sharing issues."""
    if not hasattr(_thread_local, 'supabase_client'):
        options = ClientOptions(
            schema=schema,
            postgrest_client_timeout=30,  # 30 second timeout for PostgREST
            storage_client_timeout=30,    # 30 second timeout for Storage
            function_client_timeout=30,   # 30 second timeout for Functions
            auto_refresh_token=True,      # Auto-refresh tokens
            persist_session=False,        # Don't persist sessions across processes
        )
        _thread_local.supabase_client = create_client(url, service_key, options)
    return _thread_local.supabase_client
```

### 2. Automatic Retry Mechanism

**File: `pp-worker/database.py`**

- Added `@retry_on_ssl_error()` decorator to all database functions
- Automatically retries operations up to 3 times on SSL-related errors
- Uses exponential backoff (1s, 2s, 3s delays)
- Clears thread-local client on retry to force new connection

```python
@retry_on_ssl_error(max_retries=3, delay=1.0)
def get_task_result_file_paths(task_result_ids: List[int], table_name: str = 'task_results'):
    # Function implementation with automatic retry on SSL errors
```

### 3. Storage Layer Updates

**File: `pp-worker/storage.py`**

- Updated `SuperbaseStorage` class to use thread-local clients
- Added retry decorators to file download/upload operations
- Removed instance-level Supabase client to prevent connection sharing

### 4. Enhanced Error Detection

The retry mechanism detects various SSL and connection-related errors:
- `decryption failed or bad record mac`
- `ssl error`
- `connection reset`
- `connection aborted`
- `broken pipe`

## Benefits

1. **Eliminated SSL Errors**: No more shared connection issues between processes
2. **Improved Reliability**: Automatic retry mechanism handles transient network issues
3. **Better Performance**: Dedicated connections per process reduce contention
4. **Graceful Degradation**: Failed operations are retried before giving up

## Configuration

The retry mechanism can be configured by modifying the decorator parameters:

```python
@retry_on_ssl_error(max_retries=5, delay=2.0)  # 5 retries with 2s base delay
```

## Monitoring

The fix includes enhanced logging:
- Debug logs when new clients are created
- Warning logs for retry attempts
- Error logs when all retries are exhausted

## Testing

To verify the fix is working:

1. Monitor worker logs for SSL error messages (should be eliminated)
2. Check for retry warning messages (indicates fix is working)
3. Verify task completion rates improve
4. Monitor database connection counts

## Backward Compatibility

- All existing function signatures remain unchanged
- Legacy global client is maintained for compatibility
- No changes required to calling code

## Performance Impact

- Minimal overhead from retry mechanism
- Slight memory increase due to per-thread clients
- Improved overall reliability outweighs small performance cost

## Future Improvements

1. **Connection Pooling**: Implement proper connection pooling at the client level
2. **Health Checks**: Add periodic connection health checks
3. **Metrics**: Add detailed metrics for retry rates and success rates
4. **Circuit Breaker**: Implement circuit breaker pattern for persistent failures
