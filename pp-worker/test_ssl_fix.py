#!/usr/bin/env python3
"""
Test script to verify SSL fix implementation.
This script tests the thread-local client creation and retry mechanism.
"""

import threading
import time
import logging
from database import get_supabase_client, retry_on_ssl_error

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_thread_local_clients():
    """Test that each thread gets its own Supabase client."""
    print("Testing thread-local client creation...")
    
    clients = {}
    
    def get_client_in_thread(thread_id):
        client = get_supabase_client()
        clients[thread_id] = id(client)  # Store object ID
        print(f"Thread {thread_id}: Client ID = {id(client)}")
    
    # Create multiple threads
    threads = []
    for i in range(3):
        thread = threading.Thread(target=get_client_in_thread, args=(i,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Check that all clients are different
    unique_clients = len(set(clients.values()))
    print(f"Created {unique_clients} unique clients across {len(clients)} threads")
    
    if unique_clients == len(clients):
        print("✅ Thread-local client test PASSED")
        return True
    else:
        print("❌ Thread-local client test FAILED")
        return False

@retry_on_ssl_error(max_retries=2, delay=0.1)
def test_retry_mechanism():
    """Test the retry mechanism with a simulated SSL error."""
    print("Testing retry mechanism...")
    
    # This will be called multiple times due to the retry decorator
    if not hasattr(test_retry_mechanism, 'call_count'):
        test_retry_mechanism.call_count = 0
    
    test_retry_mechanism.call_count += 1
    print(f"Retry attempt #{test_retry_mechanism.call_count}")
    
    # Simulate SSL error on first two calls
    if test_retry_mechanism.call_count <= 2:
        raise Exception("SSL error: decryption failed or bad record mac")
    
    print("✅ Operation succeeded after retries")
    return "success"

def test_retry_failure():
    """Test retry mechanism when all attempts fail."""
    print("Testing retry failure scenario...")

    @retry_on_ssl_error(max_retries=2, delay=0.1)
    def always_fail():
        raise Exception("SSL error: decryption failed or bad record mac")

    try:
        always_fail()
        print("❌ Expected exception was not raised")
        return False
    except Exception as e:
        print(f"✅ Retry failure test PASSED - Exception: {e}")
        return True

def test_eof_error():
    """Test retry mechanism with EOF protocol violation error."""
    print("Testing EOF protocol violation error...")

    @retry_on_ssl_error(max_retries=2, delay=0.1)
    def eof_error():
        # Simulate the exact error from the logs
        import httpcore
        raise httpcore.WriteError("EOF occurred in violation of protocol (_ssl.c:2406)")

    try:
        eof_error()
        print("❌ Expected exception was not raised")
        return False
    except Exception as e:
        print(f"✅ EOF error test PASSED - Exception caught and retried: {e}")
        return True

def main():
    """Run all tests."""
    print("=" * 50)
    print("SSL Fix Verification Tests")
    print("=" * 50)

    tests_passed = 0
    total_tests = 4
    
    # Test 1: Thread-local clients
    if test_thread_local_clients():
        tests_passed += 1
    
    print("\n" + "-" * 30 + "\n")
    
    # Test 2: Retry mechanism success
    try:
        result = test_retry_mechanism()
        if result == "success":
            print("✅ Retry mechanism test PASSED")
            tests_passed += 1
        else:
            print("❌ Retry mechanism test FAILED")
    except Exception as e:
        print(f"❌ Retry mechanism test FAILED with exception: {e}")
    
    print("\n" + "-" * 30 + "\n")
    
    # Test 3: Retry failure
    if test_retry_failure():
        tests_passed += 1

    print("\n" + "-" * 30 + "\n")

    # Test 4: EOF protocol violation error
    if test_eof_error():
        tests_passed += 1

    print("\n" + "=" * 50)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests PASSED! SSL fix is working correctly.")
        return 0
    else:
        print("⚠️  Some tests FAILED. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
